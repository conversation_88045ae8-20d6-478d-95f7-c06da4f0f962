package agentpool

import (
	"context"
	"strings"

	"go.goms.io/aks/rp/resourceprovider/sharedlib/consts"
	"go.goms.io/aks/rp/toolkit/log"
)

// FilterBackendPoolsForExclusion filters backend pools based on whether the agent pool is excluded.
// Excluded agent pools should only join outbound pools, not inbound pools.
func FilterBackendPoolsForExclusion(ctx context.Context, backendpoolIDs map[string]struct{}, isExcludedAgentPool bool) map[string]struct{} {
	if !isExcludedAgentPool {
		// Non-excluded agent pools can join all pools
		return backendpoolIDs
	}

	// Excluded agent pools should only join outbound pools
	filteredPools := make(map[string]struct{})
	for poolID := range backendpoolIDs {
		if IsOutboundBackendPool(poolID) {
			filteredPools[poolID] = struct{}{}
		} else {
			log.GetLogger(ctx).Infof(ctx, "Excluding inbound backend pool %s for excluded agent pool", poolID)
		}
	}
	return filteredPools
}

// IsOutboundBackendPool checks if a backend pool ID corresponds to an outbound pool.
// Pool IDs are in format: /subscriptions/.../resourceGroups/.../providers/Microsoft.Network/loadBalancers/kubernetes/backendAddressPools/{poolName}
func IsOutboundBackendPool(poolID string) bool {
	// Check if the pool ID contains the outbound pool names
	return strings.Contains(poolID, "/"+consts.SlbOutboundBackendPoolName) ||
		strings.Contains(poolID, "/"+consts.SlbOutboundBackendPoolNameIPv6)
}
